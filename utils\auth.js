// 用户认证和权限管理工具

/**
 * 获取用户token
 */
export function getToken() {
	return uni.getStorageSync('user_token')
}

/**
 * 设置用户token
 */
export function setToken(token, expiryTime = null) {
	return uni.setStorageSync('user_token', token)

	// 记录token保存时间
	const saveTime = Date.now()
	uni.setStorageSync('token_save_time', saveTime)

	// 如果提供了过期时间，保存它
	if (expiryTime) {
		uni.setStorageSync('token_expiry', expiryTime)
	} else {
		// 默认24小时后过期
		const defaultExpiry = saveTime + (24 * 60 * 60 * 1000)
		uni.setStorageSync('token_expiry', defaultExpiry)
	}

	console.log('Token已保存，过期时间:', new Date(uni.getStorageSync('token_expiry')))
}

/**
 * 移除用户token
 */
export function removeToken() {
	uni.removeStorageSync('user_token')
	uni.removeStorageSync('user_info')
	uni.removeStorageSync('token_save_time')
	uni.removeStorageSync('token_expiry')
	console.log('Token和相关信息已清除')
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
	return uni.getStorageSync('user_info')
}

/**
 * 设置用户信息
 */
export function setUserInfo(userInfo) {
	return uni.setStorageSync('user_info', userInfo)
}

/**
 * 检查用户是否已登录
 */
export function isLoggedIn() {
	const token = getToken()
	const userInfo = getUserInfo()

	if (!token || !userInfo) {
		return false
	}

	// 检查token是否过期
	if (isTokenExpired()) {
		console.log('Token已过期，需要重新登录')
		// 清除过期的token和用户信息
		removeToken()
		return false
	}

	return true
}

/**
 * 检查token是否过期
 */
export function isTokenExpired() {
	const tokenExpiry = uni.getStorageSync('token_expiry')

	if (!tokenExpiry) {
		// 如果没有过期时间，检查token的保存时间
		const tokenSaveTime = uni.getStorageSync('token_save_time')
		if (!tokenSaveTime) {
			return true // 没有保存时间，认为已过期
		}

		// 默认token有效期为24小时
		const defaultExpiryTime = 24 * 60 * 60 * 1000 // 24小时
		const now = Date.now()
		return (now - tokenSaveTime) > defaultExpiryTime
	}

	// 检查具体的过期时间
	const now = Date.now()
	return now > tokenExpiry
}

/**
 * 检查token是否即将过期（1小时内）
 */
export function isTokenExpiringSoon() {
	const tokenExpiry = uni.getStorageSync('token_expiry')

	if (!tokenExpiry) {
		return false
	}

	const now = Date.now()
	const oneHour = 60 * 60 * 1000 // 1小时

	// 如果token在1小时内过期，返回true
	return (tokenExpiry - now) < oneHour && (tokenExpiry - now) > 0
}

/**
 * 获取token剩余有效时间（毫秒）
 */
export function getTokenRemainingTime() {
	const tokenExpiry = uni.getStorageSync('token_expiry')

	if (!tokenExpiry) {
		return 0
	}

	const now = Date.now()
	const remaining = tokenExpiry - now

	return Math.max(0, remaining)
}

/**
 * 格式化剩余时间显示
 */
export function formatRemainingTime() {
	const remaining = getTokenRemainingTime()

	if (remaining <= 0) {
		return '已过期'
	}

	const hours = Math.floor(remaining / (60 * 60 * 1000))
	const minutes = Math.floor((remaining % (60 * 60 * 1000)) / (60 * 1000))

	if (hours > 0) {
		return `${hours}小时${minutes}分钟`
	} else {
		return `${minutes}分钟`
	}
}

/**
 * 检查登录状态，如果未登录则跳转到登录页
 * @param {boolean} showToast 是否显示提示
 * @returns {boolean} 是否已登录
 */
export function checkLogin(showToast = true) {
	if (!isLoggedIn()) {
		let message = '请先登录'

		// 检查是否是token过期
		const token = getToken()
		if (token && isTokenExpired()) {
			message = '登录已过期，请重新登录'
		}

		if (showToast) {
			uni.showToast({
				title: message,
				icon: 'none',
				duration: 1500
			})
		}

		// 延迟跳转，确保toast显示
		setTimeout(() => {
			uni.navigateTo({
				url: '/pages/login/login'
			})
		}, showToast ? 1500 : 0)

		return false
	}
	return true
}

/**
 * 用户登出
 */
export function logout() {
	return new Promise((resolve) => {
		uni.showModal({
			title: '确认退出',
			content: '确定要退出登录吗？',
			success: (res) => {
				if (res.confirm) {
					// 清除本地存储
					removeToken()

					uni.showToast({
						title: '已退出登录',
						icon: 'success',
						duration: 1500
					})

					// 跳转到登录页
					setTimeout(() => {
						uni.reLaunch({
							url: '/pages/login/login'
						})
					}, 1500)

					resolve(true)
				} else {
					resolve(false)
				}
			}
		})
	})
}

/**
 * 页面登录检查混入
 * 在页面的 onLoad 或 onShow 中调用
 */
export const loginCheckMixin = {
	methods: {
		// 检查登录状态
		checkLoginStatus() {
			return checkLogin()
		},

		// 退出登录
		handleLogout() {
			return logout()
		}
	}
}

/**
 * 需要登录的页面列表
 */
export const protectedPages = [
	'/pages/index/index',
	'/pages/activation/activation',
	'/pages/signin/signin'
]

/**
 * 检查当前页面是否需要登录
 */
export function isProtectedPage(url) {
	// 获取页面路径（去除参数）
	const pagePath = url.split('?')[0]
	return protectedPages.some(page => pagePath.includes(page))
}

// 全局变量，用于临时禁用路由守卫
let guardDisabled = false

/**
 * 临时禁用路由守卫
 */
export function disableGuard() {
	guardDisabled = true
	// 2秒后自动恢复
	setTimeout(() => {
		guardDisabled = false
	}, 2000)
}

/**
 * 全局页面跳转拦截
 */
export function setupNavigationGuard() {
	// 拦截 navigateTo
	const originalNavigateTo = uni.navigateTo
	uni.navigateTo = function (options) {
		if (!guardDisabled && isProtectedPage(options.url) && !isLoggedIn()) {
			console.log('navigateTo被拦截:', options.url, '登录状态:', isLoggedIn())
			checkLogin()
			return
		}
		return originalNavigateTo.call(this, options)
	}

	// 拦截 switchTab
	const originalSwitchTab = uni.switchTab
	uni.switchTab = function (options) {
		if (!guardDisabled && isProtectedPage(options.url) && !isLoggedIn()) {
			console.log('switchTab被拦截:', options.url, '登录状态:', isLoggedIn())
			checkLogin()
			return
		}
		return originalSwitchTab.call(this, options)
	}

	// 拦截 reLaunch
	const originalReLaunch = uni.reLaunch
	uni.reLaunch = function (options) {
		if (!guardDisabled && isProtectedPage(options.url) && !isLoggedIn()) {
			console.log('reLaunch被拦截:', options.url, '登录状态:', isLoggedIn())
			checkLogin()
			return
		}
		return originalReLaunch.call(this, options)
	}

	// 拦截 redirectTo
	const originalRedirectTo = uni.redirectTo
	uni.redirectTo = function (options) {
		if (isProtectedPage(options.url) && !isLoggedIn()) {
			checkLogin()
			return
		}
		return originalRedirectTo.call(this, options)
	}
}

/**
 * 自动登录检查（应用启动时调用）
 */
export async function autoLoginCheck() {
	const token = getToken()
	const userInfo = getUserInfo()

	if (!token || !userInfo) {
		// 没有登录信息，跳转到登录页
		uni.reLaunch({
			url: '/pages/login/login'
		})
		return false
	}

	// 可以在这里验证token是否有效
	// 如果有后端API，可以调用验证接口
	try {
		// 这里可以调用验证token的API
		// const res = await validateToken(token)
		// if (res.code !== 200) {
		//     removeToken()
		//     uni.reLaunch({
		//         url: '/pages/login/login'
		//     })
		//     return false
		// }

		return true
	} catch (error) {
		console.error('Token验证失败:', error)
		// 验证失败，清除登录信息
		removeToken()
		uni.reLaunch({
			url: '/pages/login/login'
		})
		return false
	}
}

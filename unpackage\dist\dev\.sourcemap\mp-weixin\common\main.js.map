{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/projects/HBuilder/fq_sign_uiapp/App.vue?96f8", "uni-app:///App.vue", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/App.vue?1976", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/App.vue?58bc"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "config", "productionTip", "App", "mpType", "app", "$mount", "onLaunch", "console", "onShow", "onHide"], "mappings": ";;;;;;;;;;;;;;AAAA;AAE2D;AAG3D;AACA;AAAgC;AAAA;AALhC;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAK1DC,YAAG,CAACC,MAAM,CAACC,aAAa,GAAG,KAAK;AAChCC,YAAG,CAACC,MAAM,GAAG,KAAK;AAClB,IAAMC,GAAG,GAAG,IAAIL,YAAG,mBACdG,YAAG,EACN;AACF,UAAAE,GAAG,EAACC,MAAM,EAAE,C;;;;;;;;;;;;;ACZZ;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACa;;;AAG/D;AAC8K;AAC9K,gBAAgB,2LAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAupB,CAAgB,mrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACC3qB;AAAA,eAEA;EACAC;IACAC;;IAEA;IACA;IAEAA;EACA;EAEAC;IACAD;EACA;EAEAE;IACAF;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACpBA;AAAA;AAAA;AAAA;AAAg8B,CAAgB,o8BAAG,EAAC,C;;;;;;;;;;;ACAp9B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import App from './App'\n\n\nimport Vue from 'vue'\nimport './uni.promisify.adaptor'\nVue.config.productionTip = false\nApp.mpType = 'app'\nconst app = new Vue({\n  ...App\n})\napp.$mount()", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\r\n\timport { setupNavigationGuard } from '@/utils/auth'\r\n\r\n\texport default {\r\n\t\tonLaunch: function() {\r\n\t\t\tconsole.log('App Launch')\r\n\r\n\t\t\t// 设置导航守卫\r\n\t\t\tsetupNavigationGuard()\r\n\r\n\t\t\tconsole.log('应用启动完成，首页将自己处理登录验证')\r\n\t\t},\r\n\r\n\t\tonShow: function() {\r\n\t\t\tconsole.log('App Show')\r\n\t\t},\r\n\r\n\t\tonHide: function() {\r\n\t\t\tconsole.log('App Hide')\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/*每个页面公共css */\r\n</style>\r\n", "import mod from \"-!../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755082084835\n      var cssReload = require(\"D:/app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}